{"name": "stable-fake-usdt", "version": "1.0.0", "description": "StableFakeUSDT - توكن يحاكي USDT بسعر ثابت للمنصات اللامركزية", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "deploy:ethereum": "hardhat run scripts/deploy-stable-usdt.js --network ethereum", "deploy:goerli": "hardhat run scripts/deploy-stable-usdt.js --network goerli", "deploy:bsc": "hardhat run scripts/deploy-stable-usdt.js --network bsc", "deploy:bsc-testnet": "hardhat run scripts/deploy-stable-usdt.js --network bscTestnet", "deploy:polygon": "hardhat run scripts/deploy-stable-usdt.js --network polygon", "deploy:local": "hardhat run scripts/deploy-stable-usdt.js --network hardhat", "verify:ethereum": "hardhat verify --network ethereum", "verify:bsc": "hardhat verify --network bsc", "node": "hardhat node", "clean": "hardhat clean", "size": "hardhat size-contracts", "gas": "REPORT_GAS=true hardhat test"}, "keywords": ["solidity", "ethereum", "bsc", "defi", "uniswap", "pancakeswap", "stable-coin", "usdt"], "author": "StableFakeUSDT Developer", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^3.0.0", "@openzeppelin/contracts": "^4.9.0", "hardhat": "^2.17.0", "dotenv": "^16.3.0", "hardhat-gas-reporter": "^1.0.9", "hardhat-contract-sizer": "^2.10.0"}, "dependencies": {"ethers": "^5.7.2"}}