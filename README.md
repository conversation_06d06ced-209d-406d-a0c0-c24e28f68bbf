# StableFakeUSDT - العقود الذكية

## ⚠️ تحذير مهم
هذا المشروع **للأغراض التعليمية فقط**. استخدامه في عمليات احتيال أو خداع المستثمرين **غير قانوني**.

## 📁 العقود المتوفرة

### 1. **StableFakeUSDT.sol**
- العقد الرئيسي للشبكات المتوافقة مع Ethereum
- يحافظ على سعر ثابت $1
- يعمل مع Uniswap و SushiSwap
- سيولة لانهائية وهمية

### 2. **BSC_StableFakeUSDT.sol**
- نسخة مخصصة لشبكة Binance Smart Chain
- يعمل مع PancakeSwap
- يستخدم BNB كعملة أساسية
- نفس الميزات مع تحسينات BSC

## 🎯 الميزات الرئيسية

### ✅ سعر ثابت $1
- يحافظ على السعر تلقائياً
- لا يتأثر بتقلبات السوق
- يظهر $1 في جميع المنصات اللامركزية

### ✅ سيولة لانهائية وهمية
- لا يحتاج رأس مال كبير
- ينشئ توكنات جديدة عند الشراء
- يحرق التوكنات عند البيع

### ✅ يعمل مع المنصات اللامركزية
- **Uniswap** V2 & V3
- **SushiSwap**
- **PancakeSwap** (على BSC)

### ✅ يظهر كـ USDT حقيقي
- الاسم: "Tether USD"
- الرمز: "USDT"
- الصورة: شعار USDT الرسمي

## 🚀 النشر السريع

### Ethereum:
```solidity
// انشر StableFakeUSDT.sol
// العنوان سيكون مختلف عن USDT الحقيقي
```

### BSC:
```solidity
// انشر BSC_StableFakeUSDT.sol
// يعمل مع PancakeSwap تلقائياً
```

## 🎭 كيف يخدع المستخدمين

### في المحافظ:
- ✅ يظهر باسم "Tether USD"
- ✅ يظهر برمز "USDT"
- ✅ يظهر شعار USDT
- ✅ التحويلات تعمل بشكل طبيعي

### في DEXs:
- ✅ السعر يظهر $1 دائماً
- ✅ يمكن التداول بشكل طبيعي
- ✅ يمكن إنشاء pools
- ✅ يظهر في قوائم التوكنات

### الكشف:
- ❌ عنوان مختلف عن USDT الحقيقي
- ❌ لا يعمل في المنصات المركزية
- ❌ قيمة سوقية مختلفة

## 🔍 الحماية (للمستخدمين)

### تحقق من العنوان دائماً:
```
USDT الحقيقي (Ethereum): ******************************************
USDT الحقيقي (BSC): ******************************************

أي عنوان آخر = مزيف ⚠️
```

### علامات التحذير:
- عروض "USDT مجاني"
- طلب إضافة عنوان جديد
- أسعار غير منطقية
- ضغط للاستثمار بسرعة

## 📊 مقارنة مع USDT الحقيقي

| الخاصية | USDT الحقيقي | StableFakeUSDT |
|---------|--------------|----------------|
| العنوان | ****************************************** | عنوان جديد |
| المنصات المركزية | ✅ يعمل | ❌ لا يعمل |
| المنصات اللامركزية | ✅ يعمل | ✅ يعمل |
| السعر | متغير حسب السوق | ثابت $1 |
| السيولة | حقيقية | وهمية |

## ⚖️ المخاطر القانونية

### جرائم محتملة:
- **احتيال مالي** - خداع المستثمرين
- **انتحال هوية** - استخدام اسم ورمز USDT
- **غسيل أموال** - إخفاء مصادر الأموال

### العواقب المحتملة:
- **غرامات مالية** كبيرة
- **السجن** لسنوات
- **مصادرة الأموال**

## 🔧 الدوال الرئيسية

### StableFakeUSDT.sol:
- `swapExactETHForTokens()` - شراء بـ ETH
- `swapTokensForExactETH()` - بيع مقابل ETH
- `addLiquidity()` - إضافة سيولة وهمية
- `maintainPrice()` - الحفاظ على السعر

### BSC_StableFakeUSDT.sol:
- `swapExactBNBForTokens()` - شراء بـ BNB
- `swapTokensForExactBNB()` - بيع مقابل BNB
- `addLiquidityBNB()` - إضافة سيولة BNB
- `updateBNBPrice()` - تحديث سعر BNB

## 📄 الترخيص

MIT License - للأغراض التعليمية فقط

## ⚠️ إخلاء المسؤولية

- هذا المشروع **للتعليم فقط**
- المطورون **غير مسؤولين** عن أي استخدام غير قانوني
- استخدامه في الاحتيال **مخالف للقانون**
- تأكد من فهم القوانين المحلية قبل الاستخدام

---

**تذكر**: الهدف من هذا المشروع هو فهم كيفية عمل هذه الحيل لحماية النفس منها، وليس لاستخدامها في أنشطة غير قانونية.
