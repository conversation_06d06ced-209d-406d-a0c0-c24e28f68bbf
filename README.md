# StableFakeUSDT - العقود الذكية

## ⚠️ تحذير مهم
هذا المشروع **للأغراض التعليمية فقط**. استخدامه في عمليات احتيال أو خداع المستثمرين **غير قانوني**.

## 📁 العقود المتوفرة

### 1. **StableFakeUSDT.sol**
- العقد الرئيسي للشبكات المتوافقة مع Ethereum
- يحافظ على سعر ثابت $1
- يعمل مع Uniswap و SushiSwap
- سيولة لانهائية وهمية

### 2. **BSC_StableFakeUSDT.sol**
- نسخة مخصصة لشبكة Binance Smart Chain
- يعمل مع PancakeSwap
- يستخدم BNB كعملة أساسية
- نفس الميزات مع تحسينات BSC

## 🎯 الميزات الرئيسية

### ✅ سعر ثابت $1 (مربوط بالدولار الأمريكي)
- **1 توكن = 1 دولار أمريكي** (مثل USDT الحقيقي تماماً)
- يحافظ على السعر تلقائياً بناء على أسعار ETH/BNB الحقيقية
- لا يتأثر بتقلبات السوق
- يظهر $1 في جميع المنصات اللامركزية

### ✅ سيولة لانهائية وهمية
- لا يحتاج رأس مال كبير
- ينشئ توكنات جديدة عند الشراء
- يحرق التوكنات عند البيع

### ✅ يعمل مع المنصات اللامركزية
- **Uniswap** V2 & V3
- **SushiSwap**
- **PancakeSwap** (على BSC)

### ✅ يظهر كـ USDT حقيقي
- الاسم: "Tether USD"
- الرمز: "USDT"
- الصورة: شعار USDT الرسمي

## 💰 كيف يعمل السعر الثابت

### المبدأ الأساسي:
```
1 توكن = 1 دولار أمريكي (مثل USDT الحقيقي تماماً)
```

### على Ethereum:
```javascript
// مثال: إذا كان سعر ETH = 3000 دولار
// المستخدم يرسل 0.1 ETH
// القيمة بالدولار = 0.1 × 3000 = 300 دولار
// التوكنات المستلمة = 300 توكن

// عند البيع:
// المستخدم يبيع 300 توكن
// القيمة بالدولار = 300 دولار
// ETH المستلم = 300 ÷ 3000 = 0.1 ETH
```

### على BSC:
```javascript
// مثال: إذا كان سعر BNB = 600 دولار
// المستخدم يرسل 0.5 BNB
// القيمة بالدولار = 0.5 × 600 = 300 دولار
// التوكنات المستلمة = 300 توكن

// عند البيع:
// المستخدم يبيع 300 توكن
// القيمة بالدولار = 300 دولار
// BNB المستلم = 300 ÷ 600 = 0.5 BNB
```

## 🚀 النشر السريع في Remix

### خطوات النشر:

#### 1. افتح Remix IDE:
```
https://remix.ethereum.org
```

#### 2. إنشاء ملف جديد:
- اضغط على "Create New File"
- اسم الملف: `StableFakeUSDT.sol` (للإيثريوم)
- أو: `BSC_StableFakeUSDT.sol` (لـ BSC)

#### 3. انسخ الكود:
- انسخ محتوى الملف كاملاً
- الصق في Remix

#### 4. إعدادات المترجم:
```
Compiler Version: 0.8.0 أو أحدث
Auto compile: ✅
```

#### 5. النشر:
- اذهب إلى تبويب "Deploy & Run"
- اختر الشبكة (Ethereum أو BSC)
- اضغط "Deploy"

### Ethereum:
```solidity
// انشر StableFakeUSDT.sol
// العنوان سيكون مختلف عن USDT الحقيقي
// السعر الافتراضي: ETH = 3000 دولار
```

### BSC:
```solidity
// انشر BSC_StableFakeUSDT.sol
// يعمل مع PancakeSwap تلقائياً
// السعر الافتراضي: BNB = 600 دولار
```

## 🎭 كيف يخدع المستخدمين

### في المحافظ:
- ✅ يظهر باسم "Tether USD"
- ✅ يظهر برمز "USDT"
- ✅ يظهر شعار USDT
- ✅ التحويلات تعمل بشكل طبيعي

### في DEXs:
- ✅ السعر يظهر $1 دائماً
- ✅ يمكن التداول بشكل طبيعي
- ✅ يمكن إنشاء pools
- ✅ يظهر في قوائم التوكنات

### الكشف:
- ❌ عنوان مختلف عن USDT الحقيقي
- ❌ لا يعمل في المنصات المركزية
- ❌ قيمة سوقية مختلفة

## 🔍 الحماية (للمستخدمين)

### تحقق من العنوان دائماً:
```
USDT الحقيقي (Ethereum): ******************************************
USDT الحقيقي (BSC): ******************************************

أي عنوان آخر = مزيف ⚠️
```

### علامات التحذير:
- عروض "USDT مجاني"
- طلب إضافة عنوان جديد
- أسعار غير منطقية
- ضغط للاستثمار بسرعة

## 📊 مقارنة مع USDT الحقيقي

| الخاصية | USDT الحقيقي | StableFakeUSDT |
|---------|--------------|----------------|
| العنوان | ****************************************** | عنوان جديد |
| المنصات المركزية | ✅ يعمل | ❌ لا يعمل |
| المنصات اللامركزية | ✅ يعمل | ✅ يعمل |
| السعر | متغير حسب السوق | ثابت $1 |
| السيولة | حقيقية | وهمية |

## ⚖️ المخاطر القانونية

### جرائم محتملة:
- **احتيال مالي** - خداع المستثمرين
- **انتحال هوية** - استخدام اسم ورمز USDT
- **غسيل أموال** - إخفاء مصادر الأموال

### العواقب المحتملة:
- **غرامات مالية** كبيرة
- **السجن** لسنوات
- **مصادرة الأموال**

## 🔧 الدوال الرئيسية

### StableFakeUSDT.sol:
- `swapExactETHForTokens()` - شراء بـ ETH (حسب سعر ETH الحقيقي)
- `swapTokensForExactETH()` - بيع مقابل ETH (1 توكن = 1 دولار)
- `getETHPriceUSD()` - الحصول على سعر ETH بالدولار
- `updateETHPrice()` - تحديث سعر ETH (المالك فقط)

### BSC_StableFakeUSDT.sol:
- `swapExactBNBForTokens()` - شراء بـ BNB (حسب سعر BNB الحقيقي)
- `swapTokensForExactBNB()` - بيع مقابل BNB (1 توكن = 1 دولار)
- `getBNBPriceUSD()` - الحصول على سعر BNB بالدولار
- `updateBNBPrice()` - تحديث سعر BNB (المالك فقط)

## 📄 الترخيص

MIT License - للأغراض التعليمية فقط

## ⚠️ إخلاء المسؤولية

- هذا المشروع **للتعليم فقط**
- المطورون **غير مسؤولين** عن أي استخدام غير قانوني
- استخدامه في الاحتيال **مخالف للقانون**
- تأكد من فهم القوانين المحلية قبل الاستخدام

---

**تذكر**: الهدف من هذا المشروع هو فهم كيفية عمل هذه الحيل لحماية النفس منها، وليس لاستخدامها في أنشطة غير قانونية.
