# StableFakeUSDT - توكن مستقر بسعر ثابت $1

## ⚠️ تحذير مهم
هذا المشروع **للأغراض التعليمية فقط**. استخدامه في عمليات احتيال أو خداع المستثمرين **غير قانوني** ويمكن أن يؤدي إلى عواقب قانونية خطيرة.

## 🎯 الهدف من المشروع
إنشاء توكن يحاكي USDT بالخصائص التالية:
- ✅ **سعر ثابت $1** في جميع الأوقات
- ✅ **يعمل مع المنصات اللامركزية** (Uniswap, SushiSwap, PancakeSwap)
- ✅ **سيولة لانهائية وهمية** بدون الحاجة لرأس مال كبير
- ✅ **يظهر في المحافظ** باسم "Tether USD" ورمز "USDT"
- ❌ **لا يعمل في المنصات المركزية** (Binance, Coinbase, etc.)

## 🏗️ البنية التقنية

### العقود الذكية
1. **StableFakeUSDT.sol** - للشبكات المتوافقة مع Ethereum
2. **BSC_StableFakeUSDT.sol** - لشبكة Binance Smart Chain

### الميزات الرئيسية
- **سعر ثابت**: يحافظ على سعر $1 تلقائياً
- **سيولة وهمية**: لا يحتاج سيولة حقيقية كبيرة
- **تداول لامركزي**: يعمل مع جميع DEXs
- **خزانة احتياطية**: ETH/BNB للحفاظ على الاستقرار

## 🚀 التثبيت والإعداد

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. إعداد المتغيرات البيئية
```bash
cp .env.example .env
# عدّل .env وأضف مفاتيحك
```

### 3. تجميع العقود
```bash
npm run compile
```

## 📡 النشر

### على Ethereum Mainnet
```bash
npm run deploy:ethereum
```

### على BSC Mainnet
```bash
npm run deploy:bsc
```

### على Testnet (للتجربة)
```bash
npm run deploy:goerli
npm run deploy:bsc-testnet
```

## 🔧 كيفية الاستخدام

### 1. إضافة التوكن للمحفظة مع الصورة
بعد النشر، استخدم المعلومات التالية:
- **العنوان**: [عنوان العقد المنشور]
- **الرمز**: USDT
- **الاسم**: Tether USD
- **الخانات العشرية**: 6 (Ethereum) أو 18 (BSC)
- **صورة التوكن**: https://cryptologos.cc/logos/tether-usdt-logo.png

#### طرق إضافة التوكن مع الصورة:

**أ) استخدام الصفحة المولدة تلقائياً:**
```bash
# بعد النشر، افتح الملف المولد:
add-token-ethereum.html  # للإيثيريوم
add-token-bsc.html       # لـ BSC
```

**ب) إضافة يدوية لـ MetaMask:**
1. افتح MetaMask
2. اذهب إلى "Import tokens"
3. أدخل عنوان العقد
4. سيظهر الاسم والرمز تلقائياً مع الصورة

**ج) استخدام JavaScript:**
```javascript
await ethereum.request({
    method: 'wallet_watchAsset',
    params: {
        type: 'ERC20',
        options: {
            address: 'CONTRACT_ADDRESS',
            symbol: 'USDT',
            decimals: 6,
            image: 'https://cryptologos.cc/logos/tether-usdt-logo.png'
        }
    }
});
```

### 2. التداول في Uniswap
1. اذهب إلى [app.uniswap.org](https://app.uniswap.org)
2. أضف التوكن المخصص بالعنوان
3. سيظهر السعر $1 دائماً
4. يمكن التبديل مع ETH أو أي توكن آخر

### 3. التداول في PancakeSwap (BSC)
1. اذهب إلى [pancakeswap.finance](https://pancakeswap.finance)
2. أضف التوكن المخصص
3. التداول مقابل BNB بسعر ثابت

### 4. إنشاء السيولة
```javascript
// مثال لإضافة سيولة وهمية
await contract.addLiquidity(
    ethers.utils.parseUnits("1000", 6), // 1000 USDT
    ethers.utils.parseEther("0.33"),    // 0.33 ETH
    deployer.address
);
```

## 🎭 كيف يعمل الخداع

### في المحافظ العادية
- ✅ يظهر باسم "Tether USD"
- ✅ يظهر برمز "USDT" 
- ✅ يظهر شعار USDT
- ✅ التحويلات تعمل بشكل طبيعي

### في المنصات اللامركزية
- ✅ يمكن إنشاء أزواج تداول
- ✅ السعر يظهر $1 دائماً
- ✅ يمكن التبديل مع عملات أخرى
- ✅ يظهر في قوائم التوكنات

### الكشف والحماية
- ❌ **عنوان مختلف** عن USDT الحقيقي
- ❌ **لا يعمل** في المنصات المركزية
- ❌ **قيمة سوقية مختلفة** في CoinGecko/CMC

## 🔍 الفروق عن USDT الحقيقي

| الخاصية | USDT الحقيقي | StableFakeUSDT |
|---------|--------------|----------------|
| العنوان | ****************************************** | عنوان جديد |
| المنصات المركزية | ✅ يعمل | ❌ لا يعمل |
| المنصات اللامركزية | ✅ يعمل | ✅ يعمل |
| السعر | متغير حسب السوق | ثابت $1 |
| السيولة | حقيقية | وهمية |

## ⚖️ المخاطر القانونية

### جرائم محتملة
- **احتيال مالي** - خداع المستثمرين
- **انتحال هوية** - استخدام اسم ورمز USDT
- **غسيل أموال** - إخفاء مصادر الأموال
- **مخالفة قوانين الأوراق المالية**

### العواقب المحتملة
- **غرامات مالية** كبيرة
- **السجن** لسنوات
- **مصادرة الأموال**
- **حظر من الخدمات المالية**

## 🛡️ الحماية (للمستخدمين)

### كيفية تجنب الخداع
1. **تحقق من العنوان** دائماً
2. **استخدم المواقع الرسمية** فقط
3. **لا تثق في الروابط** من مصادر غير موثوقة
4. **تحقق من Etherscan/BSCScan**

### علامات التحذير
- عروض "USDT مجاني"
- طلب إضافة عنوان جديد
- أسعار غير منطقية
- ضغط للاستثمار بسرعة

## 🔧 الصيانة والتطوير

### تحديث السعر
```javascript
// تحديث سعر BNB (BSC فقط)
await contract.updateBNBPrice(ethers.utils.parseEther("350")); // 350 USD
```

### إدارة الخزانة
```javascript
// حقن ETH
await contract.injectETH({ value: ethers.utils.parseEther("10") });

// سحب ETH
await contract.withdrawETH(ethers.utils.parseEther("5"));
```

### إضافة منصات جديدة
```javascript
await contract.addAuthorizedDEX("0x_NEW_DEX_ADDRESS");
```

## 📊 الإحصائيات

### استهلاك Gas
- **النشر**: ~2,500,000 gas
- **التحويل**: ~65,000 gas
- **التبديل**: ~120,000 gas
- **إضافة سيولة**: ~180,000 gas

### الشبكات المدعومة
- ✅ Ethereum Mainnet
- ✅ Binance Smart Chain
- ✅ Polygon (قابل للتطوير)
- ✅ Arbitrum (قابل للتطوير)

## 🤝 المساهمة

هذا المشروع للأغراض التعليمية. إذا كنت تريد المساهمة:
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## 📄 الترخيص

MIT License - للأغراض التعليمية فقط

## ⚠️ إخلاء المسؤولية

- هذا المشروع **للتعليم فقط**
- المطورون **غير مسؤولين** عن أي استخدام غير قانوني
- استخدامه في الاحتيال **مخالف للقانون**
- تأكد من فهم القوانين المحلية قبل الاستخدام

---

**تذكر**: الهدف من هذا المشروع هو فهم كيفية عمل هذه الحيل لحماية النفس منها، وليس لاستخدامها في أنشطة غير قانونية.
