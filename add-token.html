<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة USDT للمحفظة</title>
    <link rel="icon" href="https://cryptologos.cc/logos/tether-usdt-logo.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #26d0ce 0%, #1a2980 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .token-info {
            padding: 30px;
            text-align: center;
        }
        
        .token-logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            border-radius: 50%;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
            text-align: right;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #26d0ce;
        }
        
        .info-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #666;
            font-family: monospace;
            word-break: break-all;
        }
        
        .buttons-section {
            padding: 0 30px 30px;
        }
        
        .button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
        }
        
        .btn-metamask {
            background: linear-gradient(135deg, #f6851b 0%, #e2761b 100%);
            color: white;
        }
        
        .btn-bsc {
            background: linear-gradient(135deg, #f0b90b 0%, #d4a309 100%);
            color: white;
        }
        
        .btn-uniswap {
            background: linear-gradient(135deg, #ff007a 0%, #e6006e 100%);
            color: white;
        }
        
        .btn-pancake {
            background: linear-gradient(135deg, #1fc7d4 0%, #1bb3c0 100%);
            color: white;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            margin: 20px 30px;
            border-radius: 10px;
            border-right: 4px solid #ffc107;
        }
        
        .network-tabs {
            display: flex;
            background: #f8f9fa;
            margin: 0 30px;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            background: transparent;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            background: #26d0ce;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🪙 إضافة USDT</h1>
            <p>أضف التوكن لمحفظتك بنقرة واحدة</p>
        </div>
        
        <div class="token-info">
            <img src="https://cryptologos.cc/logos/tether-usdt-logo.png" alt="USDT Logo" class="token-logo">
            
            <div class="network-tabs">
                <button class="tab active" onclick="switchNetwork('ethereum')">Ethereum</button>
                <button class="tab" onclick="switchNetwork('bsc')">BSC</button>
            </div>
            
            <div id="ethereum-content" class="tab-content active">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">اسم التوكن</div>
                        <div class="info-value">Tether USD</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الرمز</div>
                        <div class="info-value">USDT</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الخانات العشرية</div>
                        <div class="info-value">6</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الشبكة</div>
                        <div class="info-value">Ethereum Mainnet</div>
                    </div>
                </div>
                
                <div class="info-item" style="margin: 20px 0;">
                    <div class="info-label">عنوان العقد</div>
                    <div class="info-value" id="eth-address">سيتم تحديثه بعد النشر</div>
                </div>
            </div>
            
            <div id="bsc-content" class="tab-content">
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">اسم التوكن</div>
                        <div class="info-value">Tether USD</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الرمز</div>
                        <div class="info-value">USDT</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الخانات العشرية</div>
                        <div class="info-value">18</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">الشبكة</div>
                        <div class="info-value">Binance Smart Chain</div>
                    </div>
                </div>
                
                <div class="info-item" style="margin: 20px 0;">
                    <div class="info-label">عنوان العقد</div>
                    <div class="info-value" id="bsc-address">سيتم تحديثه بعد النشر</div>
                </div>
            </div>
        </div>
        
        <div class="buttons-section">
            <button class="button btn-metamask" onclick="addToMetaMask()">
                🦊 إضافة لـ MetaMask
            </button>
            
            <button class="button btn-uniswap" onclick="openInUniswap()">
                🦄 فتح في Uniswap
            </button>
            
            <button class="button btn-pancake" onclick="openInPancakeSwap()" id="pancake-btn" style="display: none;">
                🥞 فتح في PancakeSwap
            </button>
            
            <button class="button" style="background: #6c757d; color: white;" onclick="copyAddress()">
                📋 نسخ العنوان
            </button>
        </div>
        
        <div class="warning">
            <strong>⚠️ تحذير مهم:</strong><br>
            هذا المشروع للأغراض التعليمية فقط. لا تستخدمه في عمليات احتيال أو خداع المستثمرين. 
            الاستخدام غير القانوني قد يؤدي إلى عواقب قانونية خطيرة.
        </div>
    </div>

    <script>
        // معلومات التوكن (يتم تحديثها بعد النشر)
        const TOKEN_INFO = {
            ethereum: {
                address: "CONTRACT_ADDRESS_ETHEREUM",
                symbol: "USDT",
                decimals: 6,
                image: "https://cryptologos.cc/logos/tether-usdt-logo.png",
                chainId: 1
            },
            bsc: {
                address: "CONTRACT_ADDRESS_BSC",
                symbol: "USDT", 
                decimals: 18,
                image: "https://cryptologos.cc/logos/tether-usdt-logo.png",
                chainId: 56
            }
        };
        
        let currentNetwork = 'ethereum';
        
        // تبديل الشبكة
        function switchNetwork(network) {
            currentNetwork = network;
            
            // تحديث التبويبات
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            event.target.classList.add('active');
            document.getElementById(network + '-content').classList.add('active');
            
            // إظهار/إخفاء زر PancakeSwap
            const pancakeBtn = document.getElementById('pancake-btn');
            if (network === 'bsc') {
                pancakeBtn.style.display = 'block';
            } else {
                pancakeBtn.style.display = 'none';
            }
        }
        
        // إضافة التوكن لـ MetaMask
        async function addToMetaMask() {
            try {
                if (typeof window.ethereum === 'undefined') {
                    alert('يرجى تثبيت MetaMask أولاً');
                    return;
                }
                
                const tokenInfo = TOKEN_INFO[currentNetwork];
                
                const wasAdded = await window.ethereum.request({
                    method: 'wallet_watchAsset',
                    params: {
                        type: 'ERC20',
                        options: {
                            address: tokenInfo.address,
                            symbol: tokenInfo.symbol,
                            decimals: tokenInfo.decimals,
                            image: tokenInfo.image
                        }
                    }
                });
                
                if (wasAdded) {
                    alert('✅ تم إضافة USDT بنجاح لـ MetaMask!');
                } else {
                    alert('❌ تم إلغاء إضافة التوكن');
                }
            } catch (error) {
                console.error('خطأ في إضافة التوكن:', error);
                alert('حدث خطأ في إضافة التوكن');
            }
        }
        
        // فتح في Uniswap
        function openInUniswap() {
            const tokenInfo = TOKEN_INFO.ethereum;
            const uniswapURL = `https://app.uniswap.org/#/swap?inputCurrency=ETH&outputCurrency=${tokenInfo.address}`;
            window.open(uniswapURL, '_blank');
        }
        
        // فتح في PancakeSwap
        function openInPancakeSwap() {
            const tokenInfo = TOKEN_INFO.bsc;
            const pancakeURL = `https://pancakeswap.finance/swap?inputCurrency=BNB&outputCurrency=${tokenInfo.address}`;
            window.open(pancakeURL, '_blank');
        }
        
        // نسخ العنوان
        function copyAddress() {
            const tokenInfo = TOKEN_INFO[currentNetwork];
            navigator.clipboard.writeText(tokenInfo.address).then(() => {
                alert('✅ تم نسخ العنوان!');
            }).catch(() => {
                alert('❌ فشل في نسخ العنوان');
            });
        }
        
        // تحديث عناوين العقود (يتم استدعاؤها بعد النشر)
        function updateAddresses(ethAddress, bscAddress) {
            TOKEN_INFO.ethereum.address = ethAddress;
            TOKEN_INFO.bsc.address = bscAddress;
            
            document.getElementById('eth-address').textContent = ethAddress;
            document.getElementById('bsc-address').textContent = bscAddress;
        }
        
        // فحص إذا كانت العناوين محدثة
        window.addEventListener('load', () => {
            if (TOKEN_INFO.ethereum.address.includes('CONTRACT_ADDRESS')) {
                document.getElementById('eth-address').innerHTML = '<span style="color: #dc3545;">⚠️ لم يتم النشر بعد</span>';
            }
            if (TOKEN_INFO.bsc.address.includes('CONTRACT_ADDRESS')) {
                document.getElementById('bsc-address').innerHTML = '<span style="color: #dc3545;">⚠️ لم يتم النشر بعد</span>';
            }
        });
    </script>
</body>
</html>
