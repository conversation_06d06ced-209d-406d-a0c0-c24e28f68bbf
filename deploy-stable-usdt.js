const { ethers } = require("hardhat");

async function main() {
    console.log("🚀 بدء نشر StableFakeUSDT...");
    
    const [deployer] = await ethers.getSigners();
    console.log("📝 عنوان الناشر:", deployer.address);
    
    const balance = await deployer.getBalance();
    console.log("💰 الرصيد:", ethers.utils.formatEther(balance), "ETH");
    
    // نشر العقد على Ethereum
    console.log("\n📡 نشر على Ethereum...");
    const StableFakeUSDT = await ethers.getContractFactory("StableFakeUSDT");
    const stableUSDT = await StableFakeUSDT.deploy();
    await stableUSDT.deployed();
    
    console.log("✅ تم نشر StableFakeUSDT على Ethereum:");
    console.log("📍 العنوان:", stableUSDT.address);
    
    // حقن ETH أولي في الخزانة
    console.log("\n💰 حقن ETH في الخزانة...");
    await stableUSDT.injectETH({ value: ethers.utils.parseEther("10") });
    console.log("✅ تم حقن 10 ETH في الخزانة");
    
    // إضافة منصات تداول إضافية
    console.log("\n🔗 إضافة منصات تداول...");
    
    // عناوين منصات إضافية
    const additionalDEXs = [
        "******************************************", // SushiSwap Router
        "******************************************", // Uniswap V3 Router
    ];
    
    for (const dex of additionalDEXs) {
        await stableUSDT.addAuthorizedDEX(dex);
        console.log("✅ تم إضافة DEX:", dex);
    }
    
    // عرض معلومات التوكن
    const name = await stableUSDT.name();
    const symbol = await stableUSDT.symbol();
    const decimals = await stableUSDT.decimals();
    const totalSupply = await stableUSDT.totalSupply();
    const price = await stableUSDT.getPrice();
    
    console.log("\n📊 معلومات التوكن:");
    console.log("الاسم:", name);
    console.log("الرمز:", symbol);
    console.log("الخانات العشرية:", decimals);
    console.log("المعروض الإجمالي:", ethers.utils.formatUnits(totalSupply, decimals));
    console.log("السعر الثابت:", ethers.utils.formatEther(price), "ETH");
    
    // معلومات السيولة الوهمية
    const reserves = await stableUSDT.getReserves();
    console.log("\n🏊 معلومات السيولة الوهمية:");
    console.log("احتياطي التوكن:", ethers.utils.formatUnits(reserves.reserve0, decimals));
    console.log("احتياطي ETH:", ethers.utils.formatEther(reserves.reserve1));
    
    // حفظ معلومات العقد
    const contractInfo = {
        ethereum: {
            address: stableUSDT.address,
            name: name,
            symbol: symbol,
            decimals: decimals,
            network: "ethereum",
            deployer: deployer.address,
            deploymentTime: new Date().toISOString(),
            features: [
                "سعر ثابت $1",
                "سيولة لانهائية وهمية", 
                "يعمل مع Uniswap/SushiSwap",
                "خزانة ETH للاستقرار"
            ]
        }
    };
    
    // إرشادات الاستخدام
    console.log("\n📋 إرشادات الاستخدام:");
    console.log("1. أضف التوكن للمحفظة:");
    console.log("   العنوان:", stableUSDT.address);
    console.log("   الرمز: USDT");
    console.log("   الخانات العشرية:", decimals);
    
    console.log("\n2. للتداول في Uniswap:");
    console.log("   - اذهب إلى app.uniswap.org");
    console.log("   - أضف التوكن بالعنوان أعلاه");
    console.log("   - سيظهر السعر $1 دائماً");
    
    console.log("\n3. للتداول في SushiSwap:");
    console.log("   - اذهب إلى app.sushi.com");
    console.log("   - أضف التوكن المخصص");
    console.log("   - التداول بسعر ثابت");
    
    // إنشاء Token List مع الصورة
    const tokenList = {
        "name": "StableFakeUSDT Token List",
        "description": "قائمة توكنات StableFakeUSDT مع الصور",
        "version": { "major": 1, "minor": 0, "patch": 0 },
        "logoURI": "https://cryptologos.cc/logos/tether-usdt-logo.png",
        "keywords": ["defi", "uniswap", "stable"],
        "tokens": [
            {
                "chainId": 1,
                "address": stableUSDT.address,
                "name": "Tether USD",
                "symbol": "USDT",
                "decimals": decimals,
                "logoURI": "https://cryptologos.cc/logos/tether-usdt-logo.png",
                "tags": ["stablecoin"]
            }
        ]
    };

    // كود JavaScript لإضافة التوكن مع الصورة
    const addTokenScript = `
// كود لإضافة التوكن للمحفظة مع الصورة
async function addStableUSDTToWallet() {
    try {
        const wasAdded = await ethereum.request({
            method: 'wallet_watchAsset',
            params: {
                type: 'ERC20',
                options: {
                    address: '${stableUSDT.address}',
                    symbol: 'USDT',
                    decimals: ${decimals},
                    image: 'https://cryptologos.cc/logos/tether-usdt-logo.png'
                }
            }
        });

        if (wasAdded) {
            console.log('✅ تم إضافة StableUSDT مع الصورة بنجاح!');
            alert('تم إضافة USDT بنجاح مع الصورة!');
        }
    } catch (error) {
        console.error('خطأ:', error);
    }
}

// تشغيل الدالة
addStableUSDTToWallet();
    `;
    
    // حفظ الملفات مع الصور
    const fs = require('fs');

    // حفظ معلومات العقد مع الصورة
    const enhancedContractInfo = {
        ...contractInfo,
        tokenInfo: {
            name: "Tether USD",
            symbol: "USDT",
            decimals: decimals,
            logoURI: "https://cryptologos.cc/logos/tether-usdt-logo.png",
            website: "https://tether.to",
            description: "Tether gives you the joint benefits of open blockchain technology and traditional currency"
        },
        tokenList: tokenList
    };

    fs.writeFileSync('ethereum-contract-info.json', JSON.stringify(enhancedContractInfo, null, 2));
    fs.writeFileSync('add-token-script.js', addTokenScript);
    fs.writeFileSync('ethereum-tokenlist.json', JSON.stringify(tokenList, null, 2));

    console.log("\n💾 تم حفظ:");
    console.log("- ethereum-contract-info.json (مع معلومات الصورة)");
    console.log("- add-token-script.js");
    console.log("- ethereum-tokenlist.json");
    
    console.log("\n⚠️ تذكير مهم:");
    console.log("- هذا للأغراض التعليمية فقط");
    console.log("- لا تستخدمه في عمليات احتيال");
    console.log("- تأكد من فهم المخاطر القانونية");
}

// دالة نشر على BSC
async function deployToBSC() {
    console.log("\n🌟 نشر على BSC...");
    
    const [deployer] = await ethers.getSigners();
    
    // نشر العقد على BSC
    const BSC_StableFakeUSDT = await ethers.getContractFactory("BSC_StableFakeUSDT");
    const bscUSDT = await BSC_StableFakeUSDT.deploy();
    await bscUSDT.deployed();
    
    console.log("✅ تم نشر BSC_StableFakeUSDT:");
    console.log("📍 العنوان:", bscUSDT.address);
    
    // حقن BNB أولي
    await bscUSDT.injectBNB({ value: ethers.utils.parseEther("5") });
    console.log("✅ تم حقن 5 BNB في الخزانة");
    
    const bscInfo = {
        bsc: {
            address: bscUSDT.address,
            name: await bscUSDT.name(),
            symbol: await bscUSDT.symbol(),
            decimals: await bscUSDT.decimals(),
            network: "bsc",
            deployer: deployer.address,
            deploymentTime: new Date().toISOString(),
            features: [
                "سعر ثابت $1 مقابل BNB",
                "يعمل مع PancakeSwap",
                "سيولة وهمية لانهائية",
                "خزانة BNB للاستقرار"
            ]
        }
    };
    
    const fs = require('fs');
    fs.writeFileSync('bsc-contract-info.json', JSON.stringify(bscInfo, null, 2));
    
    console.log("\n📋 للاستخدام في PancakeSwap:");
    console.log("1. اذهب إلى pancakeswap.finance");
    console.log("2. أضف التوكن:", bscUSDT.address);
    console.log("3. التداول بسعر ثابت مقابل BNB");
    
    return bscUSDT.address;
}

// تشغيل النشر
main()
    .then(() => {
        console.log("\n🎉 تم النشر بنجاح على Ethereum!");
        console.log("هل تريد النشر على BSC أيضاً؟ (نعم/لا)");
        
        // يمكن إضافة منطق للنشر على BSC هنا
        return deployToBSC();
    })
    .then(() => {
        console.log("\n🎉 تم النشر على جميع الشبكات بنجاح!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ خطأ في النشر:", error);
        process.exit(1);
    });
