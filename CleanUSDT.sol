// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title CleanUSDT - نسخة نظيفة من USDT المزيف
 * ⚠️ للأغراض التعليمية فقط
 */

contract CleanUSDT {
    
    // معلومات التوكن
    string public name = "Tether USD";
    string public symbol = "USDT";
    uint8 public decimals = 6;
    uint256 public totalSupply;
    
    // المالك
    address public owner;
    
    // الأرصدة والموافقات
    mapping(address => uint256) public balanceOf;
    mapping(address => mapping(address => uint256)) public allowance;
    
    // السعر والإعدادات
    uint256 public constant PRICE_USD = 1e6; // 1 USDT = 1 USD
    uint256 public ethPriceUSD = 3000e6; // سعر ETH بالدولار
    
    // حماية من إعادة الدخول
    bool private locked;
    
    // الأحداث
    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
    event PriceUpdated(string asset, uint256 newPrice);
    event Swap(address indexed user, uint256 ethAmount, uint256 tokenAmount);
    
    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }
    
    modifier noReentrant() {
        require(!locked, "Reentrant call");
        locked = true;
        _;
        locked = false;
    }
    
    constructor() {
        owner = msg.sender;
        totalSupply = 1000000000 * 10**decimals; // 1 مليار توكن
        balanceOf[msg.sender] = totalSupply;
        emit Transfer(address(0), msg.sender, totalSupply);
    }
    
    // دوال ERC20 الأساسية
    function transfer(address to, uint256 amount) public returns (bool) {
        require(balanceOf[msg.sender] >= amount, "Insufficient balance");
        
        balanceOf[msg.sender] -= amount;
        balanceOf[to] += amount;
        
        emit Transfer(msg.sender, to, amount);
        return true;
    }
    
    function approve(address spender, uint256 amount) public returns (bool) {
        allowance[msg.sender][spender] = amount;
        emit Approval(msg.sender, spender, amount);
        return true;
    }
    
    function transferFrom(address from, address to, uint256 amount) public returns (bool) {
        require(balanceOf[from] >= amount, "Insufficient balance");
        require(allowance[from][msg.sender] >= amount, "Insufficient allowance");
        
        balanceOf[from] -= amount;
        balanceOf[to] += amount;
        allowance[from][msg.sender] -= amount;
        
        emit Transfer(from, to, amount);
        return true;
    }
    
    // دوال التبديل
    function swapETHForTokens() external payable noReentrant {
        require(msg.value > 0, "Invalid ETH amount");
        
        // حساب قيمة ETH بالدولار
        uint256 usdValue = (msg.value * ethPriceUSD) / 1e18;
        
        // كل دولار = 1 توكن
        uint256 tokenAmount = usdValue;
        
        // إنشاء توكنات جديدة
        totalSupply += tokenAmount;
        balanceOf[msg.sender] += tokenAmount;
        
        emit Transfer(address(0), msg.sender, tokenAmount);
        emit Swap(msg.sender, msg.value, tokenAmount);
    }
    
    function swapTokensForETH(uint256 tokenAmount) external noReentrant {
        require(balanceOf[msg.sender] >= tokenAmount, "Insufficient tokens");
        
        // حساب قيمة التوكنات بالدولار
        uint256 usdValue = tokenAmount; // كل توكن = 1 دولار
        
        // تحويل إلى ETH
        uint256 ethAmount = (usdValue * 1e18) / ethPriceUSD;
        require(address(this).balance >= ethAmount, "Insufficient ETH");
        
        // حرق التوكنات
        balanceOf[msg.sender] -= tokenAmount;
        totalSupply -= tokenAmount;
        
        // إرسال ETH
        payable(msg.sender).transfer(ethAmount);
        
        emit Transfer(msg.sender, address(0), tokenAmount);
        emit Swap(msg.sender, ethAmount, tokenAmount);
    }
    
    // دوال إدارية
    function updateETHPrice(uint256 newPrice) external onlyOwner {
        require(newPrice > 0, "Invalid price");
        ethPriceUSD = newPrice;
        emit PriceUpdated("ETH", newPrice);
    }
    
    function getETHPrice() external view returns (uint256) {
        return ethPriceUSD;
    }
    
    function getPrice() external pure returns (uint256) {
        return PRICE_USD;
    }
    
    // دوال إضافية
    function mint(address to, uint256 amount) external onlyOwner {
        totalSupply += amount;
        balanceOf[to] += amount;
        emit Transfer(address(0), to, amount);
    }
    
    function burn(uint256 amount) external {
        require(balanceOf[msg.sender] >= amount, "Insufficient balance");
        
        balanceOf[msg.sender] -= amount;
        totalSupply -= amount;
        
        emit Transfer(msg.sender, address(0), amount);
    }
    
    function emergencyWithdraw() external onlyOwner {
        payable(owner).transfer(address(this).balance);
    }
    
    // دالة لاستقبال ETH
    receive() external payable {}
    
    // دالة للحصول على معلومات التوكن
    function getTokenInfo() external view returns (
        string memory _name,
        string memory _symbol,
        uint8 _decimals,
        uint256 _totalSupply,
        uint256 _ethPrice
    ) {
        return (name, symbol, decimals, totalSupply, ethPriceUSD);
    }
}
