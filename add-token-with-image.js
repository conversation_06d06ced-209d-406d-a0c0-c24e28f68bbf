/**
 * نص JavaScript لإضافة التوكن مع الصورة للمحافظ المختلفة
 */

// معلومات التوكن (يتم تحديثها بعد النشر)
const TOKEN_INFO = {
    ethereum: {
        address: "CONTRACT_ADDRESS_ETHEREUM",
        symbol: "USDT",
        decimals: 6,
        image: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    },
    bsc: {
        address: "CONTRACT_ADDRESS_BSC", 
        symbol: "USDT",
        decimals: 18,
        image: "https://cryptologos.cc/logos/tether-usdt-logo.png"
    }
};

/**
 * إضافة التوكن لـ MetaMask
 */
async function addToMetaMask(network = 'ethereum') {
    try {
        if (typeof window.ethereum === 'undefined') {
            alert('يرجى تثبيت MetaMask أولاً');
            return;
        }

        const tokenInfo = TOKEN_INFO[network];
        
        const wasAdded = await window.ethereum.request({
            method: 'wallet_watchAsset',
            params: {
                type: 'ERC20',
                options: {
                    address: tokenInfo.address,
                    symbol: tokenInfo.symbol,
                    decimals: tokenInfo.decimals,
                    image: tokenInfo.image
                }
            }
        });

        if (wasAdded) {
            console.log('✅ تم إضافة التوكن بنجاح إلى MetaMask!');
            alert('تم إضافة USDT بنجاح!');
        } else {
            console.log('❌ تم إلغاء إضافة التوكن');
        }
    } catch (error) {
        console.error('خطأ في إضافة التوكن:', error);
        alert('حدث خطأ في إضافة التوكن');
    }
}

/**
 * إضافة التوكن لـ Trust Wallet (عبر WalletConnect)
 */
async function addToTrustWallet(network = 'ethereum') {
    try {
        const tokenInfo = TOKEN_INFO[network];
        
        // رابط Trust Wallet لإضافة التوكن
        const trustWalletURL = `https://link.trustwallet.com/add_asset?asset=c${getChainId(network)}_t${tokenInfo.address}`;
        
        // فتح الرابط
        window.open(trustWalletURL, '_blank');
        
        console.log('✅ تم فتح Trust Wallet لإضافة التوكن');
    } catch (error) {
        console.error('خطأ في إضافة التوكن لـ Trust Wallet:', error);
    }
}

/**
 * الحصول على Chain ID
 */
function getChainId(network) {
    const chainIds = {
        'ethereum': 1,
        'bsc': 56,
        'goerli': 5,
        'bsc-testnet': 97
    };
    return chainIds[network] || 1;
}

/**
 * إنشاء QR Code لإضافة التوكن
 */
function generateTokenQR(network = 'ethereum') {
    const tokenInfo = TOKEN_INFO[network];
    const chainId = getChainId(network);
    
    // بيانات التوكن في صيغة JSON
    const tokenData = {
        address: tokenInfo.address,
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        chainId: chainId,
        image: tokenInfo.image
    };
    
    // تحويل إلى JSON string
    const qrData = JSON.stringify(tokenData);
    
    // يمكن استخدام مكتبة QR code هنا
    console.log('بيانات QR Code:', qrData);
    
    return qrData;
}

/**
 * إنشاء رابط مباشر لإضافة التوكن
 */
function generateAddTokenLink(network = 'ethereum') {
    const tokenInfo = TOKEN_INFO[network];
    const chainId = getChainId(network);
    
    // رابط عام لإضافة التوكن
    const baseURL = 'https://metamask.github.io/Add-Token/';
    const params = new URLSearchParams({
        address: tokenInfo.address,
        symbol: tokenInfo.symbol,
        decimals: tokenInfo.decimals,
        image: tokenInfo.image,
        chainId: chainId
    });
    
    return `${baseURL}?${params.toString()}`;
}

/**
 * إضافة التوكن لقائمة Uniswap
 */
function addToUniswapList(network = 'ethereum') {
    const tokenInfo = TOKEN_INFO[network];
    
    // رابط Uniswap مع التوكن
    const uniswapURL = `https://app.uniswap.org/#/swap?inputCurrency=ETH&outputCurrency=${tokenInfo.address}`;
    
    window.open(uniswapURL, '_blank');
    console.log('✅ تم فتح Uniswap مع التوكن');
}

/**
 * إضافة التوكن لقائمة PancakeSwap
 */
function addToPancakeSwapList() {
    const tokenInfo = TOKEN_INFO.bsc;
    
    // رابط PancakeSwap مع التوكن
    const pancakeURL = `https://pancakeswap.finance/swap?inputCurrency=BNB&outputCurrency=${tokenInfo.address}`;
    
    window.open(pancakeURL, '_blank');
    console.log('✅ تم فتح PancakeSwap مع التوكن');
}

/**
 * تحديث عناوين العقود بعد النشر
 */
function updateContractAddresses(addresses) {
    if (addresses.ethereum) {
        TOKEN_INFO.ethereum.address = addresses.ethereum;
    }
    if (addresses.bsc) {
        TOKEN_INFO.bsc.address = addresses.bsc;
    }
    
    console.log('✅ تم تحديث عناوين العقود:', TOKEN_INFO);
}

/**
 * إنشاء HTML لواجهة إضافة التوكن
 */
function createAddTokenInterface() {
    const html = `
    <div style="max-width: 500px; margin: 20px auto; padding: 20px; border: 1px solid #ddd; border-radius: 10px; font-family: Arial;">
        <h2 style="text-align: center; color: #333;">إضافة USDT للمحفظة</h2>
        
        <div style="text-align: center; margin: 20px 0;">
            <img src="https://cryptologos.cc/logos/tether-usdt-logo.png" alt="USDT" style="width: 64px; height: 64px;">
        </div>
        
        <div style="margin: 15px 0;">
            <strong>الاسم:</strong> Tether USD<br>
            <strong>الرمز:</strong> USDT<br>
            <strong>العنوان:</strong> <span id="token-address">سيتم تحديثه بعد النشر</span>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="addToMetaMask('ethereum')" style="background: #f6851b; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                إضافة لـ MetaMask (Ethereum)
            </button>
            <br>
            <button onclick="addToMetaMask('bsc')" style="background: #f0b90b; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                إضافة لـ MetaMask (BSC)
            </button>
            <br>
            <button onclick="addToUniswapList('ethereum')" style="background: #ff007a; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                فتح في Uniswap
            </button>
            <br>
            <button onclick="addToPancakeSwapList()" style="background: #1fc7d4; color: white; border: none; padding: 10px 20px; margin: 5px; border-radius: 5px; cursor: pointer;">
                فتح في PancakeSwap
            </button>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0;">
            <strong>⚠️ تحذير:</strong> هذا للأغراض التعليمية فقط. لا تستخدمه في عمليات احتيال.
        </div>
    </div>
    `;
    
    return html;
}

/**
 * إنشاء صفحة ويب كاملة لإضافة التوكن
 */
function createTokenWebsite() {
    const html = `
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إضافة USDT للمحفظة</title>
        <link rel="icon" href="https://cryptologos.cc/logos/tether-usdt-logo.png">
    </head>
    <body style="margin: 0; padding: 20px; background: #f5f5f5;">
        ${createAddTokenInterface()}
        
        <script>
            ${addToMetaMask.toString()}
            ${addToTrustWallet.toString()}
            ${addToUniswapList.toString()}
            ${addToPancakeSwapList.toString()}
            ${getChainId.toString()}
            
            // تحديث عناوين العقود هنا بعد النشر
            const TOKEN_INFO = ${JSON.stringify(TOKEN_INFO, null, 2)};
        </script>
    </body>
    </html>
    `;
    
    return html;
}

// تصدير الدوال للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        addToMetaMask,
        addToTrustWallet,
        addToUniswapList,
        addToPancakeSwapList,
        updateContractAddresses,
        createAddTokenInterface,
        createTokenWebsite,
        TOKEN_INFO
    };
}
