// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * ⚠️ تحذير: هذا العقد للأغراض التعليمية فقط
 * استخدامه في عمليات احتيال غير قانوني
 */
contract FakeUSDT is ERC20, Ownable {
    
    // نفس المعلومات الخاصة بـ USDT الحقيقي
    uint8 private _decimals = 6; // USDT الحقيقي يستخدم 6 decimals
    
    constructor() ERC20("Tether USD", "USDT") {
        // إنشاء مليار توكن للمالك
        _mint(msg.sender, ********** * 10**_decimals);
    }
    
    /**
     * @dev إرجاع عدد الخانات العشرية (6 مثل USDT الحقيقي)
     */
    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev إنشاء توكنات جديدة (للمالك فقط)
     */
    function mint(address to, uint256 amount) external onlyOwner {
        _mint(to, amount);
    }
    
    /**
     * @dev حرق توكنات (للمالك فقط)
     */
    function burn(uint256 amount) external onlyOwner {
        _burn(msg.sender, amount);
    }
    
    /**
     * @dev تجميد حساب معين (للمالك فقط)
     */
    mapping(address => bool) private _blacklisted;
    
    function blacklist(address account) external onlyOwner {
        _blacklisted[account] = true;
    }
    
    function unblacklist(address account) external onlyOwner {
        _blacklisted[account] = false;
    }
    
    function isBlacklisted(address account) external view returns (bool) {
        return _blacklisted[account];
    }
    
    /**
     * @dev تعديل دالة التحويل لمنع الحسابات المجمدة
     */
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal virtual override {
        require(!_blacklisted[from], "Sender is blacklisted");
        require(!_blacklisted[to], "Recipient is blacklisted");
        super._beforeTokenTransfer(from, to, amount);
    }
    
    /**
     * @dev دالة طوارئ لسحب أي توكنات عالقة
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
    
    /**
     * @dev دالة لسحب ETH العالق
     */
    function withdrawETH() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
    
    /**
     * @dev دالة لتحديث معلومات التوكن (اختيارية)
     */
    function updateTokenInfo(string memory newName, string memory newSymbol) external onlyOwner {
        // ملاحظة: هذا غير ممكن في ERC20 العادي
        // يحتاج تطبيق مخصص
    }
}

/**
 * @title عقد محسن لمحاكاة USDT بشكل أكثر دقة
 */
contract AdvancedFakeUSDT is ERC20, Ownable {
    
    uint8 private _decimals = 6;
    
    // محاكاة خصائص USDT الحقيقي
    bool public deprecated = false;
    address public upgradedAddress;
    
    // رسوم التحويل (مثل USDT الحقيقي القديم)
    uint256 public basisPointsRate = 0;
    uint256 public maximumFee = 0;
    
    constructor() ERC20("Tether USD", "USDT") {
        _mint(msg.sender, ********** * 10**_decimals);
    }
    
    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev محاكاة دالة deprecate الموجودة في USDT الحقيقي
     */
    function deprecate(address _upgradedAddress) external onlyOwner {
        deprecated = true;
        upgradedAddress = _upgradedAddress;
    }
    
    /**
     * @dev محاكاة دوال USDT الحقيقي
     */
    function issue(uint256 amount) external onlyOwner {
        _mint(owner(), amount);
    }
    
    function redeem(uint256 amount) external onlyOwner {
        _burn(owner(), amount);
    }
    
    /**
     * @dev تحويل مع رسوم (مثل USDT القديم)
     */
    function transferWithFee(address to, uint256 value) external returns (bool) {
        uint256 fee = (value * basisPointsRate) / 10000;
        if (fee > maximumFee) {
            fee = maximumFee;
        }
        
        uint256 sendAmount = value - fee;
        
        _transfer(msg.sender, to, sendAmount);
        if (fee > 0) {
            _transfer(msg.sender, owner(), fee);
        }
        
        return true;
    }
    
    /**
     * @dev تحديث معدل الرسوم
     */
    function setParams(uint256 newBasisPoints, uint256 newMaxFee) external onlyOwner {
        require(newBasisPoints <= 20, "Fee too high");
        require(newMaxFee <= 50 * 10**_decimals, "Max fee too high");
        
        basisPointsRate = newBasisPoints;
        maximumFee = newMaxFee;
    }
}
